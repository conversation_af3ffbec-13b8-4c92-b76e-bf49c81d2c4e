<div class="content">
    <div class="container">
<!--<pre>
  <?php // print_r($customerlist)?>
  <?php //print_r($recordlist)?>
  <?php //print_r($inqs)?>
</pre>-->
      <div class="row">
        <?php require_once('required/inquiry_headbar.php');?>

        <div class="col-md-12">
          <div class="portlet portlet-boxed">

            <div class="portlet-header">
              <h4 class="portlet-title">
                SALES INQUIRY
              </h4>
              <h4 class="portlet pull-right" >
                INQ No. : <input type="text" style="width:50%;" class="pull-right form-control" value="<?php echo $inquiry_no ?>" disabled/>
              </h4>
            </div> <!-- /.portlet-header -->

            <div class="portlet-body">
              <form method="post" id="salesupdate" action="<?php echo base_url().'index.php/create/new/inquiry/create';?>" name="salesupdate"  autocomplete="off">

               <input hidden type="text" name="txtinquiry_no" value="<?php echo $inquiry_no;?>">
               <table class="table table-bordered" width="100%">
                <tr>
                        <td align="center">Dealership Since</td>
                        <td align="center">Dealership Sales</td>
                        <td align="center">Dealership Quota</td>
                        <td align="center">Total Sales for <?php echo date('F'); ?></td>
                        
                        <td align="center">Customer Since</td>
                        <td align="center">Credit Limit</td>
                        <td align="center">Terms</td>
                        <td align="center">Balance</td>
                    </tr>
                    <tr>
                      <td align="center"><span id="txtdealsince"></span></td>
                      <td align="center"><span id="txtdealsales"></span></td>
                      <td align="center"><span id="txtdealquota"></span></td>
                      <td align="center"><span id="txtmonthlysales"></span></td>
                      
                      <td align="center"><span id="txttermssince"></span></td>
                      <td align="center"><span id="txtcreditlimit2"></span></td>
                      <td align="center"><span id="txttermsname"></span></td>
                      <td align="center"><span id="customerbalance"></span></td>
                    </tr>
               </table>
                <table width="100%;">
                  <tr>
                      <td align="right"><br /><h6>Sold to :</h6></td>
                        <td align="center" style="width: 45%"><br />
                          <select class="js-example-basic-single js-states" id="txtcustomerdropdown" style="width:90%;" name="txtcustomer" onchange="get_profile_rec('<?php echo base_url().'index.php/getpatient/record';?>');" required="required">
                               <option value="" >Select Customer</option>
                              <?php foreach($customerlist as $row){ ?>
                                    <option value="<?php echo $row['lsessionid'];?>" ><?php echo $row['lcompany'];?></option>
                             <?php } ?>
                            </select>
                        </td>
                        <td align="right"><br /><h6>Date : </h6></td>
                        <td align="center"><br /><input type="text" name="txtsalesdate" readonly id="datepicker3" class="form-control" style="width:90%;" value="<?php echo date("m/d/Y");?>"/></td>

                        <td align="right"><br /><h6>Sales&nbsp;Person:</h6></td>
                        <td align="center"><br />
                          <input type="hidden" id="hiddensalesperson" name="hiddensalesperson">
                          <input type="text" id="txtsalesperson" name="txtsalesperson" class="form-control" style="width:90%;" readonly>
                        </td>
                    </tr>
                    <tr>
                        <td align="right"><br /><h6>Delivery Address : </h6></td>
                        <td align="center"><br /><input type="text" name="txtsalesaddress" id="txtsalesaddress" class="form-control" style="width:90%;"  /></td>
                        <td align="right"><br /><h6>Our&nbsp;Reference:</h6></td>
                        <td align="center"><br /><input type="text" name="txtmyrefno" value="<?php echo $inquiry_myrefno; ?>" class="form-control" style="width:90%;" readonly /></td>
                        <td align="right"><br /><h6>Your&nbsp;Reference:</h6></td>
                        <td align="center"><br />
                          <select class="form-control" id="your_ref" name="txtyour_ref" style="width:90%">
                          </select>
                        </td>
                    </tr>
                    <tr>
                        <td align="right" colspan="2" style="width: 45%"><br />
                          <table style="width: 95%;">
                            <tr>
                              <td style="width: 15%;" align="center">
                                <h6>Send&nbsp;By:</h6>
                              </td>
                              <td style="width: 35%;" align="center">
                                <select class="js-example-basic-single js-states" name="txtshipped" style="width:94%;">
                                  <?php foreach($send_by as $row){ ?>
                                    <option value="<?php echo $row['lname'];?>"> <?php echo $row['lname'];?></option>
                                  <?php } ?>
                                </select>
                              </td>
                              <td style="width: 15%;" align="center">
                                <h6>Price&nbsp;Group:</h6>
                              </td>
                              <td style="width: 35%;">
                                <!---
                                <input type="text" id="txtpricegroup" name="txtpricegroup" class="form-control" style="width:88%;"  /> --->
                                <select class="js-example-basic-single js-states" id="txtpricegroup" name="txtpricegroup" style="width:94%;">
                                  <?php foreach($groupprice as $row){ ?>
                                    <option value="<?php echo $row['lname'];?>" <?php if (!empty($rec)) { if ($rec['lshipped']==$row['lname']) {echo "selected";} }?> > <?php echo $row['lname'];?></option>
                                  <?php } ?>
                                </select>

                              </td>
                            </tr>
                          </table>
                        </td>
                        <td align="right"><br /><h6>Credit&nbsp;Limit:</h6></td>
                        <td align="center"><br /><input type="text" id="txtcreditlimit" name="txtcreditlimit"  class="form-control" style="width:90%;"  /></td>
                        <td align="right"><br /><h6>Terms Strictly:</h6></td>
                        <td align="center"><br /><input type="text" name="txtsalesterms" id="txtsalesterms" class="form-control" style="width:90%;"  /></td>
                    </tr>
                    <tr>
                        <td align="right" ><br /><h6> Promise to Pay:</h6></td>
                        <td align="right" colspan="3"><br /><input type="text" name="txtpromissory" class="form-control" style="width:96%; margin-right: 5px;" placeholder="if applicable"/></td>
                        <td align="right" ><br /><h6> PO No.:</h6></td>
                        <td align="center"><br /><input type="text" name="txtpono"  class="form-control" style="width:90%;" placeholder="if applicable" /></td>
                    </tr>
                    <tr>
                        <td align="center" ><br /><h6> Remarks:</h6></td>
                        <td colspan="3"><br />
                          <!---
                          <input type="text" name="txtnote"  class="form-control" style="width:96%; margin-right: 4px;" placeholder="if applicable" />
                          --->

                          <select class="js-example-basic-single js-states" name="txtnote" style="width:94%;">
                            <option value=""> No Remark</option>
                                  <?php foreach($remark_template as $row){ ?>
                                    <option value="<?php echo $row['lname'];?>"> <?php echo $row['lname'];?></option>
                                  <?php } ?>
                                </select>

                        </td>
                        <td align="right" ><br /><h6> Inquiry Type:</h6></td>
                        <td align="center"><br />                                
                          <select class="form-control" name="txtsource" style="width:90%;" onchange="if(this.value=='AddNew'){$('#txtnewsource').show();}else{$('#txtnewsource').hide();}">
                            <?php foreach($inquiry_type as $row){ ?> 
                              <option value="<?php echo $row['lname'];?>"> <?php echo $row['lname'];?></option>
                            <?php } ?>
                            <option value="AddNew">Add New Type</option>
                          </select><br />
                          <input type="text" id="txtnewsource" name="txtnewsource" class="form-control" placeholder="Input Inquiry Type" style="width:90%; display: none;">
                        </td>
                    </tr>
                    <tr>
                        <td align="right" ><br /><h6> Urgency/Type: &nbsp;</h6></td>
                        <td align="right">
                          <select class="form-control" name="txturgency">
                            <option value="">N/A</option>
                            <option value="Urgent">Urgent</option>
                            <option value="By Schedule">By Schedule</option>
                          </select>
                        </td>
                        <td align="right" ><br /><h6> Urgency/Date:</h6></td>
                        <td align="center"><br />                                
                          <input type="text" name="txturgency_date" id="datepicker1"  class="form-control" value="<?php echo date("m/d/Y"); ?>" style="width:90%;" />
                        </td>
                    </tr>
                    <tr>
                      <td></td>
                        <td align="left"><br />
                          <?php if ($add_action==1) { ?>
                            <button type="submit" class="btn btn-secondary btn-sm" id="save_new_so" style="margin-left: 25px;" disabled="disabled">Add Inquiry</button>
                          <?php } ?>
                        </td>
                        <td colspan="4"></td>
                    </tr>

                </table>
                </form>

 <br>
           <table class="table table-striped table-responsive">
            <thead>
              <tr>
                        <th width="1%"></th>
                        <th width="9%">Qty.</th>
                        <th width="16%">Part No.</th>
                        <th>Item Code</th>
                        <th>Location</th>
                        <!-- <th>Brand</th> -->
                        <th>Description</th>
                        <th>Unit price</th>
                        <th>Amount</th>
                        <th>Remark</th>
                        <th style="text-align: right;">Approval</th>
                        <th></th>
                      </tr>
            </thead>

             <tfoot>
                    <tr>
                      <td align="right" colspan="9"><strong> Grand Total:</strong> <strong class="badge badge-secondary">0.00</strong></td>
                      <td colspan="2"></td>
                    </tr>
              </tfoot>
           </table>

          <!--  <input type="text" name="txttermssince" id="txttermssince" class="form-control" />
            <input type="text" name="txttermscode" id="txttermscode" class="form-control" />
            <input type="text" name="txttermsquota" id="txttermsquota" class="form-control" />
            <input type="text" name="txttermsname" id="txttermsname" class="form-control" /> -->
            </div> <!-- /.portlet-body -->

          </div> <!-- /.portlet -->


        </div> <!-- /.col -->

        <?php //require_once('required/inquiry_footbar.php');?>

      </div> <!-- /.row -->



    </div> <!-- /.container -->

  </div> <!-- .content -->

</div> <!-- /#wrapper -->


<div id="removeModal" class="modal fade">
    <form name="frmremove" action="<?php echo base_url().'index.php/sales/remove/submit'; ?>" method="post">
        <input type="hidden" name="txtrefno">
        <div class="modal-dialog">

          <div class="modal-content">

            <div class="modal-header">
              <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
              <h3 class="modal-title">Manage Inquiry</h3>
            </div> <!-- /.modal-header -->

            <div class="modal-body">
              <p>
                Are you sure you want to delete this Inquiry?
              </p>

            </div> <!-- /.modal-body -->

            <div class="modal-footer">
              <button type="submit" class="btn btn-primary">Yes Delete</button>
              <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div> <!-- /.modal-footer -->

          </div> <!-- /.modal-content -->

        </div><!-- /.modal-dialog -->
    </form>
      </div><!-- /.modal -->
