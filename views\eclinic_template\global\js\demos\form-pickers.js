$(function () {

  /* ==================================================================
   Select2 Examples 
   ================================================================== */

  $(".js-example-basic-single").select2({ allowClear: true, theme: 'bootstrap' })


  $(".js-example-basic-multiple").select2({ theme: 'bootstrap' })

  $(".js-example-tokenizer").select2({
    tags: true,
    tokenSeparators: [',', ' '],
    theme: 'bootstrap'
  })

  var data = [{ id: 0, text: 'enhancement' }, { id: 1, text: 'bug' }, { id: 2, text: 'duplicate' }, { id: 3, text: 'invalid' }, { id: 4, text: 'wontfix' }]

  $(".js-example-data-array").select2({
    data: data,
    theme: 'bootstrap'
  })

  $(".js-data-example-ajax").select2({
    theme: 'bootstrap',
    ajax: {
      url: "https://api.github.com/search/repositories",
      dataType: 'json',
      delay: 250,
      data: function (params) {
        return {
          q: params.term, // search term
          page: params.page
        }
      },
      processResults: function (data, page) {
        // parse the results into the format expected by Select2.
        // since we are using custom formatting functions we do not need to
        // alter the remote JSON data
        return {
          results: data.items
        }
      },
      cache: true
    },
    escapeMarkup: function (markup) { return markup }, // let our custom formatter work
    minimumInputLength: 1,
    templateResult: formatRepo, // omitted for brevity, see the source of this page
    templateSelection: formatRepoSelection // omitted for brevity, see the source of this page
  })



  /* ==================================================================
   Simple Color Picker Examples
   ================================================================== */

  $('#cp-ex-1').simplecolorpicker ()

  $('#cp-ex-2').simplecolorpicker({ 
    picker: true
  })

  $('#cp-ex-3').simplecolorpicker({ 
    picker: true
  })



  /* ==================================================================
   Time Picker Examples 
   ================================================================== */

  $('#timepicker1').timepicker ()

  $('#timepicker2').timepicker ()
  
  $('#timepicker3').timepicker ()
  
  $('#timepicker4').timepicker ()
  
	/*
  $('#timepicker3').timepicker ({ template: 'modal' })

  $('#timepicker4').timepicker ({ template: false })

  $('#timepicker5').timepicker ({ showMeridian: false })

  $('#timepicker6').timepicker ({ showSeconds: true })

	*/

  /* ==================================================================
   Date Picker Examples 
   ================================================================== */
var date = new Date();
date.setDate(date.getDate());

  $('#datepicker1').datepicker ({
    autoclose: true,
    todayHighlight: true,
    endDate: date
  })

  $('#datepicker2').datepicker ({
    autoclose: true,
    todayHighlight: true,
    endDate: date
  })

  // Only initialize datepicker3 if the field is not readonly
  if (!$('#datepicker3').prop('readonly')) {
    $('#datepicker3').datepicker ({
      autoclose: true,
      todayHighlight: true,
      endDate: date
    })
  }
  
   $('#datepicker4').datepicker ({
    autoclose: true,
    todayHighlight: true,
    endDate: date
  })
  $('#datepicker5').datepicker ({
    autoclose: true,
    todayHighlight: true,
    endDate: date
  })
   $('#datepicker8').datepicker ({
    autoclose: true,
    todayHighlight: true,
    endDate: date
  })
  $('.pat_datepicker').datepicker ({
    autoclose: true,
    todayHighlight: true,
    endDate: date
  })

$('#datepicker_check').datepicker ({
    autoclose: true,
    todayHighlight: true,
    
  })



  /* ==================================================================
   iCheck Examples
   ================================================================== */

  $('.demo-icheck input').iCheck ({
    checkboxClass: 'ui-icheck icheckbox_minimal-grey',
    radioClass: 'ui-icheck iradio_minimal-grey'
  }).on ('ifChanged', function (e) {
    $(e.currentTarget).trigger ('change')
  })  

})


/* ==================================================================
   Select2 Helper Functions
   ================================================================== */

function formatRepo (repo) {
  if (repo.loading) return repo.text

  var markup = '<div class="clearfix">' +
  '<div class="col-sm-1">' +
  '<img src="' + repo.owner.avatar_url + '" style="max-width: 100%" />' +
  '</div>' +
  '<div clas="col-sm-10">' +
  '<div class="clearfix">' +
  '<div class="col-sm-6">' + repo.full_name + '</div>' +
  '<div class="col-sm-3"><i class="fa fa-code-fork"></i> ' + repo.forks_count + '</div>' +
  '<div class="col-sm-2"><i class="fa fa-star"></i> ' + repo.stargazers_count + '</div>' +
  '</div>'

  if (repo.description) {
    markup += '<div>' + repo.description + '</div>'
  }

  markup += '</div></div>'

  return markup
}

function formatRepoSelection (repo) {
  return repo.full_name || repo.text
}

// wrap text box
$('.wrap').each(function () {
    this.setAttribute('style', 'height:' + 'overflow-y:hidden;resize:none;height:100px;');
  }).on('input', function () {
    this.style.height = 'auto';
    this.style.resize = 'none';
    this.style.overflow = 'hidden';
    this.style.height = (this.scrollHeight) + 'px';
  });

// datepicker onlick in variable first
function date(){
  $("#datepicker4").datepicker({
    dateFormat: 'mm/dd/yy'
  });
  $("#datepicker4").datepicker("show");
}

var counter = 1; // 
var limit  =  2; // form skin limit

// container form skin 1
function addInput(divName){
var wrap   = 'height:100px;resize:none;overflow-y:hidden;padding:0px;';
var first  = '<div class="col-md-2"  style="padding:2px;"><center><h6>Date</h6></center><input type="text" class="form-control " id="datepicker4" name="date[]" value="" onclick="date();"/></div>';
var second = '<div class="col-md-2"  style="padding:2px;"><center><h6>Dianosis</h6></center><textarea name="diagnosis[]" class="form-control wrap"  style='+wrap+'> </textarea></div>';
var third  = '<div class="col-md-2"  style="padding:2px;"><center><h6>Observation</h6></center><textarea name="observation[]" class="form-control wrap"  style='+wrap+'> </textarea></div>';
var fourth = '<div class="col-md-2"  style="padding:2px;"><center><h6>Treatment</h6></center><textarea name="treatment[]" class="form-control wrap"  style='+wrap+'> </textarea></div>';
var fifth  = '<div class="col-md-2"  style="padding:2px;"><center><h6>Home Meds</h6></center><textarea name="home_med[]" class="form-control wrap"  style='+wrap+'> </textarea></div>';
var six    = '<div class="col-md-2"  style="padding:2px;"><center><h6>Ff-up</h6></center><textarea name="ff_up[]" class="form-control wrap"  style='+wrap+'> </textarea></div>';
     if (counter == limit)  {
          alert("You have reached the limit of adding " + counter + " inputs");
     }else{
        var newdiv = document.createElement('div');
        newdiv.innerHTML = first + second + third + fourth + fifth + six ;
        document.getElementById(divName).appendChild(newdiv);
        counter++;
     }
}

// container form skin 2
function addInput_1(divName){
var wrap = 'height:auto;resize:none;overflow-y:hidden;padding:0px;';
var first  = '<div class="col-md-2"  style="padding:2px;"><center><h6>Date</h6></center><input type="text" class="form-control " id="datepicker4" name="date_1" value="" onclick="date();"/></div>';
var second = '<div class="col-md-2"  style="padding:2px;"><center><h6>Dianosis</h6></center><textarea name="diagnosis_1" class="form-control wrap"  style='+wrap+'> </textarea></div>';
var third  = '<div class="col-md-2"  style="padding:2px;"><center><h6>Observation</h6></center><textarea name="observation_1" class="form-control wrap"  style='+wrap+'> </textarea></div>';
var fourth = '<div class="col-md-2"  style="padding:2px;"><center><h6>Treatment</h6></center><textarea name="treatment_1" class="form-control wrap"  style='+wrap+'> </textarea></div>';
var fifth  = '<div class="col-md-2"  style="padding:2px;"><center><h6>Home Meds</h6></center><textarea name="home_med_1" class="form-control wrap"  style='+wrap+'> </textarea></div>';
var six    = '<div class="col-md-2"  style="padding:2px;"><center><h6>Ff-up</h6></center><textarea name="ff_up_1" class="form-control wrap"  style='+wrap+'> </textarea></div>';
var count  = '<input type="hidden" name="count">';
     if (counter == limit){
        alert("You have reached the limit of adding " + counter + " inputs");
     }else{
        var newdiv = document.createElement('div');
        newdiv.innerHTML = first + second + third + fourth + fifth + six + count;
        document.getElementById(divName).appendChild(newdiv);
        counter++;
     }
}