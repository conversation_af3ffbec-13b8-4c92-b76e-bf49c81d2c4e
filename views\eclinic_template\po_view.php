<?php
$imageloader=base_url().'application/views/eclinic_template/img/ajax-loader.gif';
?>

<style type="text/css">
  .select2-results ul li:nth-of-type(odd) {
    background-color: #ddd;
  }  
</style>

<div class="content">
    <div class="container">

	    <?php require_once('required/purchase_headbar.php')?>

        <div class="col-md-12">
          <div class="portlet portlet-boxed">

            <div class="portlet-header">
              <h4 class="portlet-title">
                PURCHASE ORDER
              </h4>
              <h4 class="portlet" style="float: right;">
                <?php if($rec['ltransaction_status']=="Pending"){ ?>
                  <a data-toggle="modal" href="#changestatus" class="btn btn-success"><b>GENERATE <u>RECEIVING REPORT</u></b></a> 
                <?php } ?>
                PO No: <?php echo $rec['lpurchaseno'];?>
              </h4>
            </div>
           
          <div class="portlet-body">


          <form method="post" id="salesupdate" action="<?php echo base_url().'index.php/purchase/record/view/'.$rec['lrefno'].'/update_rec';?>"   name="salesupdate"  autocomplete="off">
          <input type="hidden" name="txtrrno"  value="<?php echo $rec['lpurchaseno'];?>">
                <table class="table" width="20%" >
                  <tr>
                  <?php 
                $not_edited = 'edit';
                if ($this->session->userdata('main_userid')==$this->session->userdata('eclinic_userid') || $webpage_access[37]==1) { 
                  if($rec['ltransaction_status']=="Pending") {
                    $not_edited ="";
                  }
                }
                  ?>
                    <th style="border-top: 0px; text-align: center;">
                      <strong>Date:</strong>
                    </th>
                    <td style="border-top: 0px;">
                      
                      <?php if ($not_edited!="edit") { ?>
                      <input type="text" class="form-control"  name="txtsalesdate" value="<?php echo date("m/d/Y",strtotime($rec['ldate']));
                      ?>" id="datepicker" <?php echo $not_edited;?> style="width: 200px;"/>

                      <input type="submit" value="Update" class="btn btn-success">

                    <?php }else{ ?>
                      <?php echo date("m/d/Y",strtotime($rec['ldate']));?> 
                    <?php } ?>

                    

                    </td>
                 

                  </tr>
                </table>
            </form>

        <div id="salesrec" class="col-md-12">
        <form name="frmlist" method="post" action="<?php echo base_url().'index.php/purchase/record/view/'.$rec['lrefno'].'/formsubmit';?>" id="frmlist" onkeydown="return event.keyCode != 13;" autocomplete="off" >
          <input type="hidden" name="txtaction" id="txtaction">

        	<center>
            	<img src="<?php echo $imageloader;?>" id="loaderbig" style="width:50px; display:none;"/>
            </center>
          <table class="table table-striped">
            <thead>
              <tr>
                <th width="1%"><input type="checkbox" name="txtall" value="1" onClick="chktoggle_all_po(this.checked)"> </th>
                <th width="9%">Quantity</th>
                <th width="11%">Supplier</th>
                <th width="10%">ETA</th>
                <th width="12%">Original P/N</th>
                <th width="12%">Part No.</th>
                <th width="5%">Item Code</th>
                <th width="5%">Brand</th>
                <th width="5%">Description</th>
                <th width="5%">COGS</th>
                <th width="10%">RR No.</th>
                <th width="10%">Received Qty</th>
                
             </tr>
            </thead>
            <tbody>
              <!---
              <tr>
                <td colspan="<?php if($rec['ltransaction_status']!="Pending"){ echo "12"; }else{ echo "10"; } ?>">
                  <label class="control-label">
                    <input type="checkbox" id="chkreorder" name="chkreorder" onchange="change_PO_dropdown_item('<?php echo base_url().'index.php/purchase/change_PO_dropdown_item' ?>');">
                    &nbsp;For Reorder
                  </label>
                  &nbsp;&nbsp;&nbsp;&nbsp;
                  <label class="control-label">
                    <input type="checkbox" id="chknostock" name="chknostock" onchange="change_PO_dropdown_item('<?php echo base_url().'index.php/purchase/change_PO_dropdown_item' ?>');">
                    &nbsp;No Stock
                  </label>
                </td>
              </tr>
              --->
           <?php
					   $grand_tot=0;
					   foreach($itemlist as $row){
								$c_amt=$row['lsup_price'];
								$qty=$row['lqty'];
								$tot_amt=($c_amt * $qty);
								$grand_tot=$grand_tot + $tot_amt;
								$maxqty="10000000";
					   ?>
                        <tr>
                         <td>
                          <?php if($row['lreceiving_no']==""){ ?>
                            <input type="checkbox" name="txtpid[]" value="<?php echo $row['lid'];?>" onclick="show_action_po();"> 
                            <!---
                            <a data-toggle="modal" onClick="$('#txtid_remove').val(<?php echo $row['lid'];?>);" href="#removeModal" >
                            <i class="fa fa-trash"></i>
                            </a>
                            --->
                          <?php } ?>
                         </td>
                         <td>
                           <!--start add qty-->
                            <?php if($row['lreceiving_no']==""){ ?>
                           &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                           	<a data-toggle="modal" href="#edit_qty" onclick="edit_qty_item('<?php echo $row['lid']?>','<?php echo $maxqty;?>','<?php echo $row['lqty'];?>',$('#txtid').val(<?php echo $row['lid'];?>));"><?php echo $row['lqty'] ?> </a>
                            <?php } else {?>
                             <?php echo $row['lqty'] ?>
                            <?php } ?>
                           <!--end of add qty--->
                           </td>
                          <td><?php echo $row['lsupp_name'];?></td>
                          <td>
                             <?php if($row['lreceiving_no']==""){ 
                                 $txteta="";
                                if($row['leta_date']!="" && $row['leta_date']!="1970-01-01"){
                                  $txteta=date('m/d/Y',strtotime($row['leta_date']));
                                }
                                
                              ?>
                           &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <a data-toggle="modal" href="#edit_eta" onclick="document.frmupdateeta.txtpid.value='<?php echo $row['lid'];?>';document.frmupdateeta.txteta.value='<?php echo $txteta;?>';">
                                <?php 
                                  if($row['leta_date']!="" && $row['leta_date']!="1970-01-01"){
                                    echo $txteta;
                                  }else{
                                    echo "N/A";
                                  }

                               ?>
                            </a>
                            <?php } else {
                                 if($row['leta_date']!="" && $row['leta_date']!="1970-01-01"){
                                    echo date('m/d/Y',strtotime($row['leta_date']));
                                  }

                             } ?>

                            <?php 
                            
                           
                            ?>
                            
                          </td>
                          <td><?php echo $row['lopn_number'];?></td>
                          <td><?php echo $row['lpartno'];?></td>
                          <td><?php echo $row['litem_code'];?></td>
                          <td><?php echo $row['lbrand'];?></td>
                          <td><?php echo $row['ldesc'];?></td>
                          <td><?php echo number_format((float)$row['lsup_price'],2);?></td>
                            <td>
                              <a href="<?php echo base_url().'index.php/purchase/view/'.$row['lreceiving_refno'].'/rec';?>">
                                <u><?php echo $row['lreceiving_no'];?></u>
                              </a>
                            </td>
                            <td><?php echo $row['lreceiving_qty'];?></td>

                        </tr>
                        <?php } ?>

                      <?php if($rec['ltransaction_status'] == "Pending") { ?>
                    <tr>
                      <td colspan="2"><input type="number" class="form-control" id="txttblqty" name="txtqty_val" placeholder="Input Quantity"/></td>
                      <td>
                        <select class="js-example-basic-single js-states form-control" name="txtsupplier">
                          <?php foreach($supplier_list as $supp){ ?>
                            <option value="<?php echo $supp['lid']?>"><?php echo $supp['lname']?></option>
                          <?php } ?>
                        </select>
                      </td>
                      <td>
                        <input type="text" name="txtetadate" id="datepicker2" class="form-control" value="<?php echo date("m/d/Y");?>" />
                      </td>
                      <td colspan="8">
                        <select class="js-example-basic-single js-states form-control" onchange="enter_product();" name="txtitem_ref_no">
                          <option value="0" >Select Product</option>
                          <?php foreach($drop_item as $drec){ ?>
                            <option value="<?php echo $drec['lsession']?>"><?php echo 'PARTNO: '.$drec['lpartno'].'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Orig. P/N: '.$drec['lopn_number'].'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ITEMCODE: '.$drec['litemcode'].'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DESC: '.$drec['ldescription'].'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;BRAND: '.$drec['lbrand'].'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;COGS: '.number_format($drec['lcog'],2); ?></option>
                          <?php } ?>
                        </select>
                        <input type="hidden" id="hiddentexturl" value="<?php echo base_url().'index.php/purchase/record/insertitem/'.$refno."/additem";?>"/>
                        <input type="hidden" id="hiddensuccessurl" value="<?php echo base_url().'index.php/purchase/record/view/'.$refno."/rec";?>"/>
                      </td>
                    </tr>
                      <?php } ?>
                      <tr>
                        <td colspan="10">
                          <?php if($rec['ltransaction_status'] != "Posted") { ?>
                          <!--- data-toggle="modal" href="#createreciving" --->
                           <a id="btndelete_po"  class="btn btn-danger" onclick="po_deleteitem();" disabled >Delete</a>
                           <a id="btncreate_po"  class="btn btn-success" onclick="po_generatepo();" disabled>Generate Receiving</a>
                           <?php } ?>
                        </td>
                        <td align="right"><strong>Total COGS</strong></td>
                        <td><?php echo number_format($grand_tot, 2); ?></td>
                        <?php if($rec['ltransaction_status']!="Pending"){ ?>
                          <td colspan="2"></td>
                        <?php } ?>
                      </tr>
            </tbody>
           </table>

		    </form>
        </div>

            </div> <!-- /.portlet-body -->

          </div> <!-- /.portlet -->

        </div> <!-- /.col -->

        <!--sidebar -->
        <?php require_once('required/purchase_footbar.php')?>

    </div> <!-- /.container -->

  </div> <!-- .content -->

</div> <!-- /#wrapper -->
    <div id="edit_eta" class="modal fade">
    <form name="frmupdateeta" action="<?php echo base_url().'index.php/purchase/record/view/'.$rec['lrefno'];?>/updateeta" method="post">
        <div class="modal-dialog">

          <div class="modal-content">

            <div class="modal-header">
              <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
              <h3 class="modal-title">Edit ETA</h3>
            </div> <!-- /.modal-header -->

            <div class="modal-body">
             <div class="form-group">
                <label class="control-label col-md-4">ETA:</label>
                <input type="hidden"  name="txtpid"/>
                <div class="col-md-8">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="input-group">
                        <input type="text" class="form-control" id="datepicker3" placeholder="ETA"  name="txteta" style="text-align:center; font-weight:bold;">
                         
                      </div><!-- /input-group -->
                    </div>

                  </div>

                </div> <!-- /.col -->
              </div> <!-- /.form-group -->
            </div> <!-- /.modal-body -->

            <div class="modal-footer">
              <button type="submit" class="btn btn-success">Save</button>
              <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div> <!-- /.modal-footer -->

          </div> <!-- /.modal-content -->

        </div><!-- /.modal-dialog -->
    </form>
      </div><!-- /.modal -->

    <div id="deletepr_item_po" class="modal fade">
               
               <input type="hidden" value="<?php echo $refno;?>" name="porefno">
                <div class="modal-dialog">
                  <div class="modal-content">

                    <div class="modal-header">
                      <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                      <h3 class="modal-title">Delete Purchase Request</h3>
                    </div> <!-- /.modal-header -->

                    <div class="modal-body">
                      <p>
                        Are you sure you want to delete this Purchase Request?
                        <input type="hidden" name="txtpoNum" value="<?php echo $rec['lprno'];?>">
                      </p>

                    </div> <!-- /.modal-body -->

                    <div class="modal-footer">
                      <button  class="btn btn-primary" onclick="document.frmlist.txtaction.value='delete';document.frmlist.submit();">Delete</button>
                      <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    </div> <!-- /.modal-footer -->

                  </div> <!-- /.modal-content -->

                </div><!-- /.modal-dialog -->
              </div><!-- /.modal -->

              <div id="create_po" class="modal fade">
               <input type="hidden" value="<?php echo $refno;?>" name="porefno">
                <div class="modal-dialog">
                  <div class="modal-content">

                    <div class="modal-header">
                      <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                      <h3 class="modal-title">Purchase Order</h3>
                    </div> <!-- /.modal-header -->

                    <div class="modal-body">
                      <p>
                        Finalize PO? Items cannot be edited or deleted.
                      </p>

                    </div> <!-- /.modal-body -->

                    <div class="modal-footer">
                      <button onclick="document.frmlist.txtaction.value='createpo';document.frmlist.submit();"  class="btn btn-primary">Create</button>
                      <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    </div> <!-- /.modal-footer -->

                  </div> <!-- /.modal-content -->

                </div><!-- /.modal-dialog -->
              </div><!-- /.modal -->


		<div id="edit_qty" class="modal fade">
		<form name="frmremove" action="<?php echo base_url().'index.php/purchase/record/view/'.$rec['lrefno'];?>/updateqty" method="post">
        <input type="hidden" name="txtid" id="txtid">
        <div class="modal-dialog">

          <div class="modal-content">

            <div class="modal-header">
              <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
              <h3 class="modal-title">Add Quantity:</h3>
            </div> <!-- /.modal-header -->

            <div class="modal-body">
             <div class="form-group">
                <label class="control-label col-md-4">Add Quantity:</label>
				<input type="hidden"  name="txtmax_qty" id="txtmax_qty"/>
                <input type="hidden"  name="txtitem_id" id="txtitem_id"/>
                <div class="col-md-8">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="input-group">
                        <span class="input-group-btn">
                          <button class="btn btn-default" type="button" onclick="qty_minus();">-</button>
                        </span>
                        <input type="numbers" class="form-control" placeholder="Quantity"  id="txtqty_val" name="txtqty_val" onkeyup="check_qyt();" style="text-align:center; font-weight:bold;">
                         <span class="input-group-btn">
                          <button class="btn btn-default" type="button" onclick="qty_plus();">+</button>
                        </span>
                      </div><!-- /input-group -->
                    </div>

                  </div>

                </div> <!-- /.col -->
              </div> <!-- /.form-group -->
            </div> <!-- /.modal-body -->

            <div class="modal-footer">
              <button type="submit" class="btn btn-success">Save</button>
              <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div> <!-- /.modal-footer -->

          </div> <!-- /.modal-content -->

        </div><!-- /.modal-dialog -->
		</form>
      </div><!-- /.modal -->


      	<div id="changestatus" class="modal fade">
		<form name="frmremove" action="<?php echo base_url().'index.php/purchase/record/view/'.$rec['lrefno']; ?>/changestatus" method="post">

        <div class="modal-dialog">

          <div class="modal-content">

            <div class="modal-header">
              <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
              <h4 class="modal-title">Manage Purchase Order</h4>
            </div> <!-- /.modal-header -->

            <div class="modal-body">
             <div class="form-group">
					<h5 align="center">Finalize PO? Items cannot be edited or deleted.</h5>
                    </div> <!-- /.form-group -->
            </div> <!-- /.modal-body -->
            <div class="modal-footer">
              <button type="submit" class="btn btn-success" name="txtstatus" value="Posted" onClick="this.form.submit(); this.disabled=true;">Finalize</button>
              <button type="submit" class="btn btn-secondary" data-dismiss="modal" name="txtstatus" value="Pending">Close</button>
            </div> <!-- /.modal-footer -->

          </div> <!-- /.modal-content -->

        </div><!-- /.modal-dialog -->
		</form>
      </div><!-- /.modal -->


      <div id="removeModal" class="modal fade">
		<form name="frmremove" action="<?php echo base_url().'index.php/purchase/record/view/'.$rec['lrefno']; ?>/deleteitem" method="post">
        <input type="hidden" name="txtid_remove" id="txtid_remove">
        <div class="modal-dialog">
          <div class="modal-content">

            <div class="modal-header">
              <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
              <h3 class="modal-title">Manage Item</h3>
            </div> <!-- /.modal-header -->

            <div class="modal-body">
              <p>
              	Are you sure you want to delete this Item? Item/s will be deleted!.
              </p>

            </div> <!-- /.modal-body -->

            <div class="modal-footer">
              <button type="submit" class="btn btn-primary">Delete</button>
              <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div> <!-- /.modal-footer -->

          </div> <!-- /.modal-content -->

        </div><!-- /.modal-dialog -->
		</form>
      </div><!-- /.modal -->
